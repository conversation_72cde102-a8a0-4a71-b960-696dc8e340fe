#!/usr/bin/env python3
import os
import argparse
import json
from datetime import datetime
from pathlib import Path
import sys

def kebab_case(text):
    return text.lower().replace(" ", "-")

def write_file(path, content):
    with open(path, "w", encoding="utf-8") as f:
        f.write(content)

def create_command(args):
    tag = f"{args.type}-{kebab_case(args.title)}"
    base_dir = Path("changes") / tag
    base_dir.mkdir(parents=True, exist_ok=True)

    # description.md
    write_file(base_dir / "description.md", f"# {args.title}\n\n{args.desc}\n")

    # state.json
    write_file(
        base_dir / "state.json",
        json.dumps(
            {
                "currentTag": tag,
                "createdAt": datetime.utcnow().isoformat() + "Z"
            },
            indent=2
        )
    )

    # tasks.json (empty template)
    write_file(
        base_dir / "tasks.json",
        json.dumps(
            {
                tag: {
                    "tasks": []
                }
            },
            indent=2
        )
    )

    print(f"✅ Created: {base_dir}/")
    print(f"├── description.md")
    print(f"├── state.json")
    print(f"└── tasks.json")

def main():
    parser = argparse.ArgumentParser(
        description="📁 Task Agent CLI – Folder and file scaffold generator",
        prog="task-agent"
    )
    subparsers = parser.add_subparsers(dest="command")

    create_parser = subparsers.add_parser("create", help="Create a new feature or bug task folder")
    create_parser.add_argument("type", choices=["feature", "bug"], help="Type of change")
    create_parser.add_argument("--title", required=True, help="Title of the task")
    create_parser.add_argument("--desc", required=True, help="Short description")

    args = parser.parse_args()

    if args.command == "create":
        create_command(args)
    else:
        parser.print_help()

if __name__ == "__main__":
    main()
