Generate a comprehensive project rules file for:

**Project Name:** [PROJECT_NAME]
**Project Type:** [web app/mobile app/CLI tool/library/etc.]
**Primary Languages:** [list languages]
**Framework/Stack:** [main frameworks]
**Deployment Target:** [cloud platform/on-premise/package registry]

**Include These Standard Sections:**
1. Project overview and architecture
2. File organization and size limits
3. Language-specific coding standards
4. Security and secrets management
5. Testing requirements and structure
6. Development workflow and git practices
7. Documentation standards
8. Performance guidelines
9. Deployment and release process
10. Code review checklist
11. Debugging and monitoring
12. Critical violations summary
13.check existing patterns 

**Customize Based On:**
- Team size: [solo/small team/large team]
- Project phase: [prototype/MVP/production/mature]
- Security level: [public/internal/enterprise/security-critical]
- Performance needs: [basic/high-performance/real-time]

**Format as Cursor rules file with:**
- Markdown structure
- Practical code examples
- Clear enforcement mechanisms
- Actionable checklists
- Violation triggers and remediation
- Regular review schedule


📘 Output Format: Markdown  
📂 File Name: `AI_PROJECT_META.md`  
🎯 Audience: Claude Code and human contributors  
🛠️ Purpose: Acts as a working agreement + onboarding guide for safe, smart AI coding in this repo

---

### Include These Sections:

1. **Project Overview**
   - What kind of project is this? (API, CLI, library, app, monorepo, etc.)
   - Frameworks/languages used
   - High-level architecture summary (e.g., handler-based, MVC, vertical slice, layered)

2. **Folder and File Organization**
   - Key folders and what they contain (e.g., `src/`, `utils/`, `api/`, `components/`)
   - Maximum file size limits (e.g., 500 lines recommended)
   - Co-location rules (tests, styles, logic together?)

3. **Language and Coding Standards**
   - Style tools (e.g., ESLint, Black, Prettier, ruff)
   - Naming conventions, structure rules, typing requirements
   - Any validated schemas or input/output formats

4. **Security and Secrets Management**
   - Where secrets should live (e.g., `.env`, key vaults)
   - Never commit sensitive keys or credentials
   - Input validation rules (e.g., Zod, Pydantic)

5. **Testing Requirements**
   - How tests are organized (unit, integration, E2E)
   - Testing frameworks used
   - Command to run all tests
   - Coverage and failure handling

6. **Development Workflow**
   - How to start the app locally
   - Common `dev`, `build`, `test` commands
   - Required tools and environment (e.g., `Node`, `Python`, `uv`, `Docker`)

7. **Git and Version Control**
   - Branching model (e.g., feature branches, no direct to `main`)
   - Commit message style
   - Rules for staging, reviews, and merges

8. **Documentation Standards**
   - Where docs are stored (e.g., `docs/`, README headers)
   - Expectations for inline comments and public function docs
   - Auto-generated docs or special markdown conventions?

9. **Performance Guidelines**
   - Any profiling or perf-sensitive areas?
   - Do not prematurely optimize — clean code first
   - Known bottlenecks or constraints?

10. **Deployment and Release Process**
    - CI/CD system used (if any)
    - How to build and deploy
    - Pre-release checks or approvals

11. **Code Review Checklist**
    - Enforce existing patterns and utilities
    - Validate inputs and outputs
    - Don’t introduce unused abstractions
    - Follow structure, naming, and modularity rules

12. **Debugging and Monitoring**
    - Logging conventions (e.g., logger libraries, levels)
    - Monitoring tools (if any)
    - Where to find logs or metrics during dev

13. **Critical Violations Summary**
    - 🚫 Don’t delete existing code unless explicitly required
    - 🚫 Don’t ignore tests or validation results
    - 🚫 Don’t introduce breaking changes silently
    - 🚫 Don’t hardcode values that should be config
    - 🚫 Don’t catch-all exceptions — always be specific

14. **Pattern Awareness**
    - 🔍 Always check for existing code that solves the problem before writing new
    - Reuse utilities, patterns, hooks, or handlers
    - AI must confirm understanding before making structural changes

---

If you lack enough context, **ask for a repo summary or folder structure first**. If key sections (tests, CI, docs) are missing, note that clearly in the output.

