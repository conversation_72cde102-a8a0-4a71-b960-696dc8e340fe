# TechVision Design System & Style Guide

## 1. Brand Foundations
### 1.1 Purpose & Tone
- Forward-thinking, innovative, technology-first.
- Confident yet approachable language — empower clients to "transform".

### 1.2 Color Palette
| Token | HEX / Example | Tailwind Utility | Usage |
|-------|--------------|-----------------|-------|
| `brand-primary` | `#2563EB` | `blue-600` | Primary CTAs, links, accents |
| `brand-primary-light` | `#60A5FA` | `blue-400` | Hover states, gradients |
| `brand-secondary` | `#6366F1` | `indigo-600` | Secondary CTAs, headings |
| `surface-glass` | `rgba(255,255,255,0.05)` | `bg-white/5` | Card / nav backgrounds |
| `text-primary` | `#FFFFFF` | `text-white` | Headings & body |
| `text-muted` | `rgba(255,255,255,0.7)` | `text-white/70` | Sub-copy |
| Greys | `#0B0B0B – #262626` | `bg-gray-900`, `border-white/10` | Backgrounds, borders |
| State colors | `emerald-400`, `red-400`, `amber-400`, etc. | Feature icons & hovers |

Gradient recipe: `bg-gradient-to-r from-white via-blue-300 to-indigo-400`.

### 1.3 Typography
| Style | Font | Weight | Utility Examples |
|-------|------|--------|------------------|
| Display / H1-H3 | Manrope | 600 (`title-bold`), 200 (`title-light`) | `text-7xl`, `tracking-tight` |
| Body | Inter | 400 | `text-base`, `leading-relaxed` |
| Meta / Small | Inter | 500 | `text-sm`, `text-xs` |

Always set `letter-spacing: -0.03em` on display headings for compact tech feel.

### 1.4 Iconography
Font Awesome 6 — icons placed inside `.feature-icon` circular container for consistency.

---
## 2. Layout & Spacing
- Grid container: `max-w-7xl mx-auto`.
- Section side padding: `px-4 sm:px-6 lg:px-8`.
- Card corner radius: `rounded-xl` (small), `rounded-2xl / 3xl` (sections).
- Glass effect wrapper: `backdrop-filter: blur(14px) brightness(0.91)` with subtle border.
- 4-pt spacing scale drives margin & padding.

---
## 3. Components
### 3.1 Button
| Variant | Base Classes | Purpose |
|---------|--------------|---------|
| Primary | `bg-blue-600 hover:bg-blue-500 text-white pulse-glow` | Main CTA |
| Secondary (Glass) | `glass-effect bg-white/10 border border-white/10 hover:bg-white/15` | Low-emphasis |
| Gradient | `bg-gradient-to-r from-blue-600 to-indigo-600` | Section CTAs |

Shared: `rounded-xl px-8 py-4 font-medium transition-all`.

### 3.2 Card
| Type | Key Classes | Context |
|------|-------------|---------|
| Service Card | `glass-effect bg-gradient-to-br from-white/10 to-white/5` | Hero triad |
| Feature Card | Same + `group hover:border-color/30` | Services grid |
| Advantage (Stacked) | `.advantage-card` GSAP stacked animation | Why-us section |

### 3.3 Feature Icon
```html
<div class="feature-icon bg-blue-600/20 border-blue-500/30">
  <i class="fas fa-link text-blue-400"></i>
</div>
```
Color variants (`blue`, `purple`, `cyan`, etc.) map to service category.

### 3.4 Glass Panel Wrapper
`class="glass-effect bg-gradient-to-br from-white/8 to-white/3 border border-white/10 rounded-3xl"`

Add emphasis on hover:
`group hover:border-blue-500/30 transition-all duration-300`.

---
## 4. Motion
- `floating-animation` — slow 6 s vertical float on hero cards.
- `pulse-glow` — 3 s halo pulse on primary buttons.
- Scroll reveal: GSAP `ScrollTrigger` stacks `.advantage-card`, fades content from 30 px left.

Interaction animations ≤ 300 ms; scroll-based reveals 600–1000 ms.

---
## 5. Accessibility Guidelines
1. Maintain WCAG AA contrast — avoid `text-white/50` on `bg-white/5` for small text.
2. Use semantic HTML (`<nav>`, `<button>`, `<section>`).
3. Provide descriptive `aria-label` / `title` on icons & iframes.
4. Ensure focus styles (Tailwind `focus:outline-none focus:ring`) across components.

---
## 6. Usage Dos & Don'ts
| Do | Don't |
|----|-------|
| Use primary gradient heading once per viewport-height section | Mix > 2 accent colors per component |
| Reuse `glass-effect` for depth | Introduce heavy drop-shadows |
| Keep max content width ≤ `max-w-7xl` | Stretch content full-width on ultra-wide screens |

---
## 7. Design Tokens (Tailwind `tailwind.config.js` snippet)
```js
module.exports = {
  theme: {
    extend: {
      colors: {
        brand: {
          primary: '#2563EB',
          secondary: '#6366F1',
        },
      },
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
        display: ['Manrope', 'sans-serif'],
      },
    },
  },
};
```

---
_Update & expand this document as new patterns, pages, or themes are introduced._ 