---
type: "agent_requested"
description: "Universal Integration Workflow Rule"
---

# 🎯 Universal Integration Workflow Rule

## **MANDATORY WORKFLOW FOR ALL INTEGRATIONS & TASKS**

### **Phase 1: DISCOVERY (Before Any Changes)**
```bash
# REQUIRED: Understand project context
codebase-retrieval "target functionality"
view "key-files" 

# REQUIRED: Multi-pattern discovery
# Search for existing patterns, related code, dependencies
# Map all files that may be affected
```

**❌ NO CODE CHANGES WITHOUT COMPLETE DISCOVERY**

### **Phase 2: INTEGRATION MAPPING**
```bash
# REQUIRED: Map all dependencies
# Document integration points:
# - UI elements → Event handlers  
# - Function calls → Definitions
# - Data flow → Storage patterns
# - Error handling → User feedback
# - Configuration → Implementation
```

### **Phase 3: SYSTEMATIC IMPLEMENTATION**
**For EACH integration point:**

1. **Pre-change validation:**
   - Review target file for existing patterns
   - Identify integration touchpoints

2. **Apply changes with pattern consistency**
   - Follow existing code conventions
   - Maintain architectural patterns

3. **Immediate verification:**
   - Validate syntax
   - Confirm pattern consistency
   - Check cross-references

### **Phase 4: CROSS-LAYER VALIDATION**
**REQUIRED after each component:**

```bash
# Validate all file types updated
# Check JavaScript, HTML, CSS, config files
# Ensure UI elements connect to handlers
# Verify data flow integrity
# Confirm error handling paths
```

### **Phase 5: COMPLETION VERIFICATION**
```bash
# Final integration check
# Search for any missed patterns
# Validate all cross-references
# Confirm no orphaned code
# Test critical paths
```

---

## **INTEGRATION CHECKLIST**

### **✅ Discovery Complete:**
- [ ] All file types identified (.js, .html, .css, .json, etc.)
- [ ] All patterns mapped and documented
- [ ] All cross-dependencies identified
- [ ] All integration points catalogued

### **✅ Implementation Complete:**
- [ ] All layers updated (UI, logic, storage, config)
- [ ] All cross-references maintained
- [ ] All event handlers connected
- [ ] All error paths handled
- [ ] All user-facing elements updated

### **✅ Validation Complete:**
- [ ] Syntax checks pass
- [ ] Pattern consistency confirmed
- [ ] Cross-layer integration verified
- [ ] No orphaned references
- [ ] Critical paths tested

---

## **ENFORCEMENT RULE**

**🚨 ANY INTEGRATION WITHOUT THIS WORKFLOW WILL BE INCOMPLETE**

This prevents:
- ❌ Partial implementations
- ❌ Broken cross-references  
- ❌ Missing UI updates
- ❌ Orphaned code
- ❌ Integration failures

**Following this workflow ensures complete integration every time.**

---

## **VALIDATION LOOP**
```
Discovery → Mapping → Implementation → Validation → Completion Check
    ↓         ↓           ↓             ↓              ↓
  Context   Dependencies  Changes    Cross-layer    Final
  Gathering    Map       Applied     Verified      Tested
```

**Remember: Complete Discovery First, Systematic Implementation Always.**