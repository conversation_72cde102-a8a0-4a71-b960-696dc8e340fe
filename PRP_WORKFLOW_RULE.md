# 🎯 PRP Implementation Workflow Rule

## **MANDATORY WORKFLOW FOR ALL PRP IMPLEMENTATIONS**

### **Phase 1: DISCOVERY (Before Any Code Changes)**
```bash
# REQUIRED: Set project path and refresh index
set_project_path_code-index "/path/to/project"
refresh_index_code-index 
#refesh only if there where code changes since index

# REQUIRED: Multi-pattern comprehensive search
search_code_advanced_code-index "primary-pattern" --context_lines=3
search_code_advanced_code-index "secondary-pattern" --context_lines=3
search_code_advanced_code-index "ui-pattern" --file_pattern="*.html"
search_code_advanced_code-index "style-pattern" --file_pattern="*.css"

# REQUIRED: Cross-reference discovery
find_files_code-index "*target*"
get_file_summary_code-index "key-file.js"
```

**❌ DO NOT PROCEED TO CODING WITHOUT COMPLETING DISCOVERY**

### **Phase 2: PLANNING & TASK BREAKDOWN**
```bash
# REQUIRED: Create detailed task breakdown
add_tasks [comprehensive task list with 20-minute granularity]

# REQUIRED: Document all files to be modified
# REQUIRED: Map all cross-dependencies (HTML buttons → JS handlers)
# REQUIRED: Identify all user-facing elements
```

### **Phase 3: ITERATIVE IMPLEMENTATION**
**For EACH file modification:**

1. **Before editing:**
   ```bash
   view "file-to-edit.js" --search_query_regex="target-pattern"
   ```

2. **Make changes using str-replace-editor**

3. **Immediate validation:**
   ```bash
   node -c "modified-file.js"  # Syntax check
   update_tasks [{"task_id": "current", "state": "COMPLETE"}]
   ```

4. **Pattern verification:**
   ```bash
   search_code_advanced_code-index "old-pattern" --context_lines=2
   ```

### **Phase 4: CROSS-LAYER VALIDATION**
**REQUIRED after each major component:**

```bash
# JavaScript validation
search_code_advanced_code-index "target-pattern" --context_lines=3

# HTML validation  
search_code_advanced_code-index "pro-badge|PRO</span>" --file_pattern="*.html"

# Event handler validation
search_code_advanced_code-index "getElementById.*target" --context_lines=2

# Error message validation
search_code_advanced_code-index "upgrade.*pro|Pro feature" --case_sensitive=false
```

### **Phase 5: PROGRESSIVE TESTING**
**REQUIRED at each milestone:**

```bash
# Level 1: Syntax (immediate)
node -c file1.js && node -c file2.js

# Level 2: Functional (after each component)
node tests/component-specific-test.js

# Level 3: Integration (after major changes)
node tests/integration-test.js

# Level 4: End-to-end (before completion)
node tests/final-verification.js
```

### **Phase 6: FEEDBACK VALIDATION**
**REQUIRED after each major milestone:**

```bash
interactive_feedback_interactive-feedback-mcp({
    project_directory: "path",
    summary: "Specific milestone completed - request validation"
})
```

**❌ DO NOT PROCEED WITHOUT ADDRESSING FEEDBACK**

### **Phase 7: FINAL INTEGRATION CHECK**
**MANDATORY before marking PRP complete:**

```bash
# Refresh index for final validation
refresh_index_code-index

# Comprehensive pattern search
search_code_advanced_code-index "any-remaining-old-patterns"

# Cross-reference validation
# HTML elements → JS handlers
# Function calls → Function definitions  
# CSS classes → HTML elements

# Final syntax validation
find . -name "*.js" -exec node -c {} \;

# Final functional testing
node tests/comprehensive-final-test.js
```

---

## **INTEGRATION CHECKLIST (Mandatory for Each PRP)**

### **✅ Discovery Complete:**
- [ ] All file types searched (.js, .html, .css, .json)
- [ ] All patterns identified and documented
- [ ] All cross-dependencies mapped
- [ ] All user-facing elements catalogued

### **✅ Implementation Complete:**
- [ ] All syntax checks pass
- [ ] All function signatures preserved
- [ ] All imports/exports intact
- [ ] All event handlers connected

### **✅ Cross-Layer Validation:**
- [ ] JavaScript logic updated
- [ ] HTML elements updated
- [ ] CSS styling updated
- [ ] Configuration files updated

### **✅ Testing Complete:**
- [ ] Syntax validation: 100% pass
- [ ] Functional tests: 100% pass
- [ ] Integration tests: 100% pass
- [ ] End-to-end tests: 100% pass

### **✅ Production Ready:**
- [ ] No breaking changes
- [ ] Backward compatibility maintained
- [ ] Error handling preserved
- [ ] Security implications addressed

---

## **CRITICAL SUCCESS PATTERNS**

### **🔍 Search Strategy:**
1. **Primary patterns** (core functionality)
2. **Secondary patterns** (related features)
3. **UI patterns** (user-facing elements)
4. **Error patterns** (messages and prompts)
5. **Configuration patterns** (settings and config)

### **📁 File Coverage:**
- **JavaScript**: Logic and validation
- **HTML**: UI elements and structure
- **CSS**: Styling and visual indicators
- **JSON**: Configuration and manifest
- **Documentation**: Help text and guides

### **🔄 Validation Loop:**
```
Code Change → Syntax Check → Pattern Search → Test → Feedback → Iterate
```

### **⚠️ FAILURE PREVENTION:**
- **Never skip discovery phase**
- **Always validate cross-references**
- **Test immediately after each change**
- **Request feedback at every milestone**
- **Verify all file types are updated**

---

## **ENFORCEMENT RULE**

**🚨 ANY PRP THAT DOES NOT FOLLOW THIS WORKFLOW WILL REQUIRE REWORK**

This workflow prevents:
- ❌ Missing event handlers
- ❌ Leftover UI elements
- ❌ Incomplete pattern removal
- ❌ Syntax errors
- ❌ Breaking changes
- ❌ Integration failures

**Following this workflow ensures first-time success for all PRP implementations.**
