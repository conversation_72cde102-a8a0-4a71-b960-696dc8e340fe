---
description: Custom reading tool
globs: 
alwaysApply: false
---
# 📖 Custom Reading Tool - Usage Rules & Guidelines

### 🎯 **CORE RULE: Use concurrent_reader_v5.py as Default Reading Tool**

## 📖 Reading Tool Enforcement
- **MANDATORY**: Use `concurrent_reader_v5.py` for ALL file reading operations
- **NO EXCEPTIONS**: Even for "quick checks" or "small files" 
- **If you must use built-in tools**: Explicitly state why custom tool is not suitable
- **Context Agents**: NEVER use built-in read_file without first using custom tool
- **Flow**: Always read terminal output → read tags → read content files

## 🚀 Tool Capabilities

### ✅ **What It Does:**
- **Multi-file concurrent processing** - Process multiple files simultaneously
- **Individual content files** - Each file gets its own `.content.json` output
- **Ctags analysis** - Generates `.tags.json` for each file with symbol mapping
- **Organized output** - All files saved to `context/` folder (auto-created)
- **Processing summary** - Overview of all processed files
- **Large file handling** - Efficiently processes files of any size (tested up to 2,871+ lines)

### 📊 **Output Structure:**
```
context/
├── filename.content.json     # Individual file content + metadata
├── filename.tags.json        # Ctags analysis for the file
├── processing_summary.json   # Overview of all processed files
└── [additional files...]
```

## 🔧 Usage Patterns

### **1. Single File Analysis**
```bash
python concurrent_reader_v5.py filename.js
```
**Use Case:** Deep dive into one specific file

### **2. Multi-File Feature Analysis**
```bash
python concurrent_reader_v5.py js/integrations/telegram.js js/user/telegramSettings.js
```
**Use Case:** Analyze related files for a specific feature

### **3. Comprehensive Module Analysis**
```bash
python concurrent_reader_v5.py popup.js background.js config.js manifest.json content.js
```
**Use Case:** Get complete context for major components

### **4. Directory Pattern Analysis**
```bash
python concurrent_reader_v5.py js/auth/*.js js/user/*.js
```
**Use Case:** Analyze all files in specific functional areas

### **5. Workspace Discovery Mode**
```bash
python concurrent_reader_v5.py
```
**Use Case:** Auto-discover and analyze all relevant files in workspace

## 📋 When to Use the Reading Tool

### **🎯 MANDATORY Usage Scenarios:**

#### **Before Code Changes:**
- **New Feature Development** - Understand existing patterns and architecture
- **Bug Fixes** - Get complete context of affected components
- **Refactoring** - Map dependencies and relationships
- **Code Reviews** - Comprehensive understanding of changes

#### **Context Gathering:**
- **Large File Analysis** - Files >500 lines (like popup.js with 2,871 lines)
- **Multi-Component Features** - Features spanning multiple files
- **Integration Points** - Understanding how components connect
- **Legacy Code Investigation** - Unfamiliar or complex codebases

#### **Architecture Understanding:**
- **New Team Members** - Rapid codebase familiarization
- **Documentation** - Creating comprehensive code documentation
- **Dependency Mapping** - Understanding file relationships
- **API Integration** - Understanding existing integration patterns

### **⚡ Standard Workflow Integration:**

#### **Step 1: Execute Reading Tool**
```bash
# Read target files with concurrent processing
python concurrent_reader_v5.py [target_files]
```

#### **Step 2: Read Terminal Output**
- Review processing summary from terminal
- Note successful/failed file reads
- Check ctags generation status
- Identify any errors or warnings

#### **Step 3: Analyze Tags Files**
```bash
# View the generated tags for function/class mapping
cat context/filename.tags.json
```
- Study function definitions and locations
- Map class hierarchies and methods
- Identify imports and exports
- Understand code structure

#### **Step 4: Review Content Files**
```bash
# View the complete file content with metadata
cat context/filename.content.json
```
- Read full file content with line numbers
- Check file metadata (size, lines, etc.)
- Understand code patterns and implementation

#### **Step 5: Execute Task**
- Use gathered context to make informed decisions
- Implement changes following existing patterns
- Ensure backward compatibility
- Plan integration strategies

## 🏷️ Ctags Integration

### **Understanding Tags Output:**
Each `.tags.json` file contains:
- **Functions** - All function definitions with line numbers
- **Classes** - Class definitions and methods
- **Variables** - Important variable declarations
- **Imports** - Module dependencies and imports
- **Exports** - What the file exposes to other modules

### **Tags Structure Example:**
```json
[
  {
    "symbol": "handleCustomAnalysisClick",
    "kind": "f",
    "line": "    async handleCustomAnalysisClick() {",
    "pattern": "/^    async handleCustomAnalysisClick() {$/"
  }
]
```

### **Using Tags for Navigation:**
- **Symbol**: Function/class/variable name
- **Kind**: Type (f=function, c=class, v=variable, etc.)
- **Line**: Actual code line content
- **Pattern**: Regex pattern for finding the symbol

## 📁 File Organization Rules

### **Context Folder Management:**
- **Auto-Creation** - Tool automatically creates `context/` folder
- **Clean Organization** - All outputs in one dedicated folder
- **No Main Directory Pollution** - Keeps project root clean
- **Easy Cleanup** - Single folder to manage/delete if needed

### **File Naming Convention:**
- **Content Files:** `filename.content.json`
- **Tags Files:** `filename.tags.json`
- **Summary File:** `processing_summary.json`

## 🎯 Best Practices

### **1. Strategic File Selection:**
```bash
# ✅ Good - Related functionality
python concurrent_reader_v5.py js/auth/proValidator.js js/user/proStatus.js

# ❌ Avoid - Unrelated files
python concurrent_reader_v5.py popup.js README.md package.json
```

### **2. Feature-Focused Analysis:**
```bash
# ✅ Good - Complete feature context
python concurrent_reader_v5.py js/integrations/telegram.js js/user/telegramSettings.js

# ✅ Good - Core architecture
python concurrent_reader_v5.py background.js popup.js config.js
```

### **3. Performance Considerations:**
- **Concurrent Processing** - Tool handles multiple files efficiently
- **Large Files** - No size limitations (tested with 2,871+ line files)
- **Memory Efficient** - Processes files individually, not all in memory

### **4. Integration with Development:**
```bash
# Before working on Telegram feature
python concurrent_reader_v5.py js/integrations/telegram.js js/user/telegramSettings.js

# Before major refactoring
python concurrent_reader_v5.py popup.js background.js content.js

# Before API changes
python concurrent_reader_v5.py vercel-api/api/*.js vercel-api/db/*.js
```

## 🔍 Analysis Workflow

### **Step 1: Content Analysis**
1. Open relevant `.content.json` files
2. Review file metadata (lines, size, path)
3. Study code structure and patterns
4. Identify key functions and classes

### **Step 2: Tags Analysis**
1. Open corresponding `.tags.json` files
2. Map function relationships and dependencies
3. Understand class hierarchies and methods
4. Identify integration points

### **Step 3: Cross-File Analysis**
1. Compare patterns across files
2. Identify shared dependencies
3. Map data flow between components
4. Plan integration strategies

### **Step 4: Implementation Planning**
1. Use insights to plan changes
2. Follow existing patterns and conventions
3. Ensure backward compatibility
4. Plan testing strategies

## 🚨 Critical Rules

### **ALWAYS Use Before:**
- Making changes to files >300 lines
- Working with unfamiliar code
- Implementing new features
- Debugging complex issues
- Refactoring existing code

### **NEVER Skip When:**
- Working on multi-file features
- Integrating with existing APIs
- Modifying core functionality
- Working with legacy code
- Onboarding to new projects

### **File Size Guidelines:**
- **Small Files (<100 lines)** - Optional, but recommended for context
- **Medium Files (100-500 lines)** - Recommended for understanding
- **Large Files (>500 lines)** - MANDATORY for safe modifications

## 🎯 Command Line Options

### **Basic Usage:**
```bash
python concurrent_reader_v5.py [files...]
```

### **Advanced Options:**
```bash
# Control worker threads for tag generation
python concurrent_reader_v5.py --max-workers 16 file1.js file2.js

# Disable tag generation (content only)
python concurrent_reader_v5.py --no-tags file1.js

# Limit display lines in terminal (still saves full content)
python concurrent_reader_v5.py --max-lines 100 file1.js

# Auto-discover workspace files
python concurrent_reader_v5.py
```

### **Option Details:**
- **`--max-workers N`** - Control concurrency for tag generation (default: 8)
- **`--no-tags`** - Skip ctags generation, content only
- **`--max-lines N`** - Limit terminal display (0 = show all, default: 0)
- **No arguments** - Auto-discover workspace files (.py, .js, .ts, .md, etc.)

## 🎯 Success Metrics

### **Effective Usage Indicators:**
- ✅ Reduced debugging time
- ✅ Fewer integration issues
- ✅ Better code consistency
- ✅ Faster feature development
- ✅ Improved code quality

### **Tool Performance:**
- **Processing Speed** - Multiple files processed concurrently
- **Accuracy** - Complete ctags analysis with line numbers
- **Organization** - Clean, structured output
- **Reliability** - Handles files of any size

## 🔄 Maintenance

### **Context Folder Management:**
```bash
# Clean old context files periodically
rm -rf context/

# Or selectively clean specific files
rm context/old-file.*
```

### **Tool Updates:**
- Keep `concurrent_reader_v5.py` updated with latest features
- Ensure ctags is properly installed and updated
- Test with representative files after updates

## 📚 Examples

### **Example 1: Feature Development**
```bash
# Working on Discord integration
python concurrent_reader_v5.py js/integrations/discord.js js/user/discordSettings.js

# Results in:
# context/discord.content.json - Full Discord integration code
# context/discord.tags.json - All Discord functions mapped
# context/discordSettings.content.json - Settings management code
# context/discordSettings.tags.json - Settings functions mapped
```

### **Example 2: Bug Investigation**
```bash
# Investigating popup issues
python concurrent_reader_v5.py popup.js js/popup/*.js

# Provides complete popup ecosystem context
```

### **Example 3: Architecture Review**
```bash
# Understanding core architecture
python concurrent_reader_v5.py background.js popup.js config.js manifest.json

# Complete application structure analysis
```

## 🛠️ Dependencies

### **Required:**
- **Python 3.7+** - For asyncio and modern features
- **aiofiles** - For async file operations (`pip install aiofiles`)
- **ctags** - For code analysis (Universal Ctags recommended)

### **Installation:**
```bash
# Install Python dependencies
pip install aiofiles

# Install ctags (platform-specific)
# Ubuntu/Debian: sudo apt-get install universal-ctags
# macOS: brew install universal-ctags
# Windows: choco install universal-ctags
```

## 🔧 Troubleshooting

### **Common Issues:**
1. **"ctags not found"** - Install Universal Ctags for your platform
2. **Permission denied** - Check file permissions and access rights
3. **Unicode errors** - Files must be text-based, not binary
4. **Memory issues** - Use `--max-workers` to reduce concurrency

### **Solutions:**
```bash
# Check if ctags is installed
ctags --version

# Test with single file first
python concurrent_reader_v5.py single_file.js

# Disable tags if ctags unavailable
python concurrent_reader_v5.py --no-tags file.js
```

---

## 🎯 Summary

The custom reading tool is essential for:
- **Context-driven development**
- **Safe code modifications**
- **Efficient debugging**
- **Architecture understanding**
- **Team collaboration**

**Remember:** Always gather context before making changes. The reading tool provides the comprehensive understanding needed for confident, effective development.

### **Default Workflow:**
1. **Execute** `python concurrent_reader_v5.py [files]`
2. **Read** terminal output for processing summary
3. **Analyze** `.tags.json` files for code structure
4. **Review** `.content.json` files for implementation details
5. **Implement** changes with full context awareness 