#!/usr/bin/env python3
import os
import sys
import shutil
import subprocess
import re
from datetime import datetime

class UnifiedEditor:
    def __init__(self, backup_dir="backups"):
        self.backup_dir = backup_dir
        if not os.path.exists(backup_dir):
            os.makedirs(backup_dir)
    
    # ========== FILE WRITER FUNCTIONALITY ==========
    
    def create_backup(self, filepath):
        if not os.path.exists(filepath):
            return ""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = os.path.basename(filepath)
        backup_name = f"{filename}.backup_{timestamp}"
        backup_path = os.path.join(self.backup_dir, backup_name)
        try:
            shutil.copy2(filepath, backup_path)
            print(f"📁 Backup: {backup_path}")
            return backup_path
        except Exception as e:
            print(f"⚠️  Backup failed: {e}")
            return ""
    
    def write_file(self, filepath, content, create_backup=True):
        try:
            if create_backup and os.path.exists(filepath):
                self.create_backup(filepath)
            
            dir_path = os.path.dirname(filepath)
            if dir_path:
                os.makedirs(dir_path, exist_ok=True)
            
            with open(filepath, "w", encoding="utf-8") as f:
                f.write(content)
            
            print(f"✅ File written: {filepath}")
            return True
        except Exception as e:
            print(f"❌ Error: {e}")
            return False
    
    def read_file(self, filepath):
        try:
            with open(filepath, "r", encoding="utf-8") as f:
                return f.read()
        except Exception as e:
            print(f"❌ Error reading {filepath}: {e}")
            return ""
    
    def append_file(self, filepath, content):
        try:
            if os.path.exists(filepath):
                self.create_backup(filepath)
            
            with open(filepath, "a", encoding="utf-8") as f:
                f.write(content)
            
            print(f"✅ Content appended to: {filepath}")
            return True
        except Exception as e:
            print(f"❌ Error: {e}")
            return False
    
    def replace_text(self, filepath, old_text, new_text):
        try:
            content = self.read_file(filepath)
            if not content:
                return False
            
            self.create_backup(filepath)
            new_content = content.replace(old_text, new_text)
            count = content.count(old_text)
            
            if count == 0:
                print(f"❌ Text not found: {old_text[:30]}...")
                return False
            
            success = self.write_file(filepath, new_content, False)
            if success:
                print(f"🔄 Replaced {count} occurrence(s)")
            
            return success
        except Exception as e:
            print(f"❌ Error: {e}")
            return False
    
    # ========== ADVANCED EDITING FUNCTIONALITY ==========
    
    def ast_grep_replace(self, filepath, pattern, replacement, language="javascript"):
        """Use ast-grep for AST-based replacements"""
        try:
            cmd = [
                "sg", 
                "-p", pattern,
                "-r", replacement,
                "--lang", language,
                filepath
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"✅ AST replacement successful in {filepath}")
                return True
            else:
                print(f"❌ AST replacement failed: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ Error running ast-grep: {e}")
            return False
    
    def ugrep_find_lines(self, filepath, pattern, context_lines=3):
        """Use ugrep to find lines with context"""
        try:
            cmd = [
                "ugrep", 
                "-n",  # line numbers
                f"-A{context_lines}",  # after context
                f"-B{context_lines}",  # before context
                pattern,
                filepath
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                return result.stdout.split('\n')
            else:
                return []
                
        except Exception as e:
            print(f"❌ Error running ugrep: {e}")
            return []
    
    def replace_lines_by_number(self, filepath, line_replacements):
        """Replace specific lines by line number"""
        try:
            content = self.read_file(filepath)
            if not content:
                return False
            
            lines = content.split('\n')
            
            # Apply replacements (line numbers are 1-indexed)
            for line_num, new_content in line_replacements.items():
                if 1 <= line_num <= len(lines):
                    lines[line_num - 1] = new_content
                    print(f"🔄 Replaced line {line_num}")
            
            new_content = '\n'.join(lines)
            return self.write_file(filepath, new_content)
            
        except Exception as e:
            print(f"❌ Error replacing lines: {e}")
            return False
    
    def insert_lines_at_position(self, filepath, line_num, new_lines):
        """Insert lines at specific position"""
        try:
            content = self.read_file(filepath)
            if not content:
                return False
            
            lines = content.split('\n')
            
            # Insert new lines at position (1-indexed)
            if isinstance(new_lines, str):
                new_lines = [new_lines]
            
            for i, new_line in enumerate(new_lines):
                lines.insert(line_num - 1 + i, new_line)
            
            new_content = '\n'.join(lines)
            return self.write_file(filepath, new_content)
            
        except Exception as e:
            print(f"❌ Error inserting lines: {e}")
            return False
    
    def smart_function_edit(self, filepath, function_name, new_body):
        """Edit a specific function using AST matching"""
        pattern = f"function {function_name}($$$ARGS) {{ $$$BODY }}"
        replacement = f"function {function_name}($$$ARGS) {{ {new_body} }}"
        
        return self.ast_grep_replace(filepath, pattern, replacement)
    
    def add_logging_to_functions(self, filepath):
        """Add console.log to all functions"""
        pattern = "function $NAME($$$ARGS) { $$$BODY }"
        replacement = "function $NAME($$$ARGS) { console.log('🔍 Entering $NAME'); $$$BODY }"
        
        return self.ast_grep_replace(filepath, pattern, replacement)
    
    # ========== CODE STRUCTURE SEARCH ==========
    
    def find_functions(self, filepath):
        """Find all function declarations"""
        content = self.read_file(filepath)
        if not content:
            return []
        
        patterns = [
            r'function\s+(\w+)\s*\([^)]*\)\s*\{',  # function declarations
            r'(\w+)\s*:\s*function\s*\([^)]*\)\s*\{',  # object methods
            r'(\w+)\s*=\s*function\s*\([^)]*\)\s*\{',  # function expressions
            r'(\w+)\s*=>\s*\{',  # arrow functions
            r'async\s+function\s+(\w+)\s*\([^)]*\)\s*\{',  # async functions
        ]
        
        functions = []
        lines = content.split('\n')
        
        for i, line in enumerate(lines, 1):
            for pattern in patterns:
                matches = re.finditer(pattern, line)
                for match in matches:
                    func_name = match.group(1)
                    functions.append({
                        'name': func_name,
                        'line': i,
                        'type': 'function',
                        'code': line.strip()
                    })
        
        return functions
    
    def find_classes(self, filepath):
        """Find all class declarations"""
        content = self.read_file(filepath)
        if not content:
            return []
        
        patterns = [
            r'class\s+(\w+)(?:\s+extends\s+\w+)?\s*\{',  # ES6 classes
            r'function\s+(\w+)\s*\([^)]*\)\s*\{[^}]*this\.',  # Constructor functions
        ]
        
        classes = []
        lines = content.split('\n')
        
        for i, line in enumerate(lines, 1):
            for pattern in patterns:
                matches = re.finditer(pattern, line)
                for match in matches:
                    class_name = match.group(1)
                    classes.append({
                        'name': class_name,
                        'line': i,
                        'type': 'class',
                        'code': line.strip()
                    })
        
        return classes
    
    def find_variables(self, filepath):
        """Find all variable declarations"""
        content = self.read_file(filepath)
        if not content:
            return []
        
        patterns = [
            r'(?:var|let|const)\s+(\w+)',  # variable declarations
            r'(\w+)\s*=\s*[^=]',  # assignments (simple)
        ]
        
        variables = []
        lines = content.split('\n')
        
        for i, line in enumerate(lines, 1):
            # Skip comments and strings
            if line.strip().startswith('//') or line.strip().startswith('*'):
                continue
                
            for pattern in patterns:
                matches = re.finditer(pattern, line)
                for match in matches:
                    var_name = match.group(1)
                    # Skip common keywords and short vars
                    if len(var_name) > 1 and var_name not in ['if', 'for', 'while', 'return']:
                        variables.append({
                            'name': var_name,
                            'line': i,
                            'type': 'variable',
                            'code': line.strip()
                        })
        
        return variables
    
    def find_imports(self, filepath):
        """Find all import statements"""
        content = self.read_file(filepath)
        if not content:
            return []
        
        patterns = [
            r'import\s+.*?\s+from\s+[\'"]([^\'"]+)[\'"]',  # ES6 imports
            r'require\s*\(\s*[\'"]([^\'"]+)[\'"]\s*\)',  # CommonJS requires
            r'import\s*\(\s*[\'"]([^\'"]+)[\'"]\s*\)',  # Dynamic imports
        ]
        
        imports = []
        lines = content.split('\n')
        
        for i, line in enumerate(lines, 1):
            for pattern in patterns:
                matches = re.finditer(pattern, line)
                for match in matches:
                    module_name = match.group(1)
                    imports.append({
                        'name': module_name,
                        'line': i,
                        'type': 'import',
                        'code': line.strip()
                    })
        
        return imports
    
    def find_methods(self, filepath):
        """Find all method declarations within classes"""
        content = self.read_file(filepath)
        if not content:
            return []
        
        patterns = [
            r'(\w+)\s*\([^)]*\)\s*\{',  # method declarations
            r'(\w+)\s*:\s*function\s*\([^)]*\)\s*\{',  # object methods
            r'async\s+(\w+)\s*\([^)]*\)\s*\{',  # async methods
        ]
        
        methods = []
        lines = content.split('\n')
        in_class = False
        
        for i, line in enumerate(lines, 1):
            # Track if we're inside a class
            if 'class ' in line:
                in_class = True
            elif line.strip() == '}' and in_class:
                in_class = False
                
            if in_class:
                for pattern in patterns:
                    matches = re.finditer(pattern, line)
                    for match in matches:
                        method_name = match.group(1)
                        if method_name not in ['class', 'if', 'for', 'while']:
                            methods.append({
                                'name': method_name,
                                'line': i,
                                'type': 'method',
                                'code': line.strip()
                            })
        
        return methods
    
    def analyze_code_structure(self, filepath):
        """Comprehensive code structure analysis"""
        print(f"🔍 Analyzing code structure: {filepath}")
        
        functions = self.find_functions(filepath)
        classes = self.find_classes(filepath)
        variables = self.find_variables(filepath)
        imports = self.find_imports(filepath)
        methods = self.find_methods(filepath)
        
        print(f"\n📊 Code Structure Summary:")
        print(f"   Functions: {len(functions)}")
        print(f"   Classes: {len(classes)}")
        print(f"   Methods: {len(methods)}")
        print(f"   Variables: {len(variables)}")
        print(f"   Imports: {len(imports)}")
        
        return {
            'functions': functions,
            'classes': classes,
            'methods': methods,
            'variables': variables,
            'imports': imports
        }
    
    def list_backups(self):
        """List all backup files"""
        if os.path.exists(self.backup_dir):
            backups = [f for f in os.listdir(self.backup_dir) if f.endswith(".backup_") or ".backup_" in f]
            if backups:
                print(f"📁 Backups in {self.backup_dir}:")
                for backup in sorted(backups, reverse=True):
                    print(f"   {backup}")
            else:
                print("No backups found")
        else:
            print("No backup directory found")

def show_help():
    help_text = """
🛠️  Unified Editor - Writing & Advanced Editing Tool

USAGE:
    python unified_editor.py <command> [arguments]

BASIC FILE OPERATIONS:
    write <file> <content>         Write content to file
    read <file>                    Read and display file content
    append <file> <content>        Append content to file
    replace <file> <old> <new>     Replace text in file
    backup <file>                  Create backup of file
    list-backups                   List all backup files

ADVANCED EDITING:
    ast-replace <file> <pattern> <replacement>    AST-based replacement
    find-lines <file> <pattern>                   Find lines with context
    replace-line <file> <line_num> <new_content>  Replace specific line
    insert-lines <file> <line_num> <content>      Insert lines at position
    function-edit <file> <func_name> <new_body>   Edit specific function
    add-logging <file>                            Add logging to all functions

CODE STRUCTURE SEARCH:
    analyze-structure <file>                      Complete code analysis
    find-functions <file>                         Find all functions
    find-classes <file>                           Find all classes
    find-methods <file>                           Find all methods
    find-variables <file>                         Find all variables
    find-imports <file>                           Find all imports

EXAMPLES:
    # Basic operations
    python unified_editor.py write test.js "console.log('hello');"
    python unified_editor.py read test.js
    python unified_editor.py append test.js "\\n// New comment"
    
    # Advanced editing
    python unified_editor.py ast-replace test.js "var $VAR = $VALUE" "let $VAR = $VALUE"
    python unified_editor.py find-lines test.js "console.log"
    python unified_editor.py replace-line test.js 5 "// Updated line"
    python unified_editor.py function-edit test.js "myFunc" "return 'updated';"
    
    # Code structure analysis
    python unified_editor.py analyze-structure popup.js
    python unified_editor.py find-functions popup.js
    python unified_editor.py find-imports popup.js

FEATURES:
    ✅ Automatic backups before changes
    ✅ Safe file operations with error handling
    ✅ UTF-8 encoding support
    ✅ AST-based code transformations
    ✅ Pattern matching with context
    ✅ Line-specific editing
    ✅ Code structure analysis (functions, classes, methods, variables, imports)
"""
    print(help_text)

def main():
    if len(sys.argv) < 2:
        show_help()
        return
    
    command = sys.argv[1].lower()
    editor = UnifiedEditor()
    
    if command == "help" or command == "-h" or command == "--help":
        show_help()
    
    # ========== BASIC FILE OPERATIONS ==========
    elif command == "write":
        if len(sys.argv) < 4:
            print("❌ Usage: write <file> <content>")
            return
        filepath = sys.argv[2]
        content = sys.argv[3]
        editor.write_file(filepath, content)
    
    elif command == "read":
        if len(sys.argv) < 3:
            print("❌ Usage: read <file>")
            return
        filepath = sys.argv[2]
        content = editor.read_file(filepath)
        if content:
            print(content)
    
    elif command == "append":
        if len(sys.argv) < 4:
            print("❌ Usage: append <file> <content>")
            return
        filepath = sys.argv[2]
        content = sys.argv[3]
        editor.append_file(filepath, content)
    
    elif command == "replace":
        if len(sys.argv) < 5:
            print("❌ Usage: replace <file> <old_text> <new_text>")
            return
        filepath = sys.argv[2]
        old_text = sys.argv[3]
        new_text = sys.argv[4]
        editor.replace_text(filepath, old_text, new_text)
    
    elif command == "backup":
        if len(sys.argv) < 3:
            print("❌ Usage: backup <file>")
            return
        filepath = sys.argv[2]
        backup_path = editor.create_backup(filepath)
        if backup_path:
            print(f"✅ Backup created: {backup_path}")
    
    elif command == "list-backups":
        editor.list_backups()
    
    # ========== ADVANCED EDITING ==========
    elif command == "ast-replace":
        if len(sys.argv) < 5:
            print("❌ Usage: ast-replace <file> <pattern> <replacement>")
            return
        filepath, pattern, replacement = sys.argv[2], sys.argv[3], sys.argv[4]
        editor.ast_grep_replace(filepath, pattern, replacement)
    
    elif command == "find-lines":
        if len(sys.argv) < 4:
            print("❌ Usage: find-lines <file> <pattern>")
            return
        filepath, pattern = sys.argv[2], sys.argv[3]
        lines = editor.ugrep_find_lines(filepath, pattern)
        for line in lines:
            if line.strip():
                print(line)
    
    elif command == "replace-line":
        if len(sys.argv) < 5:
            print("❌ Usage: replace-line <file> <line_num> <new_content>")
            return
        filepath = sys.argv[2]
        line_num = int(sys.argv[3])
        new_content = sys.argv[4]
        editor.replace_lines_by_number(filepath, {line_num: new_content})
    
    elif command == "insert-lines":
        if len(sys.argv) < 5:
            print("❌ Usage: insert-lines <file> <line_num> <content>")
            return
        filepath = sys.argv[2]
        line_num = int(sys.argv[3])
        content = sys.argv[4]
        editor.insert_lines_at_position(filepath, line_num, content)
    
    elif command == "function-edit":
        if len(sys.argv) < 5:
            print("❌ Usage: function-edit <file> <func_name> <new_body>")
            return
        filepath, func_name, new_body = sys.argv[2], sys.argv[3], sys.argv[4]
        editor.smart_function_edit(filepath, func_name, new_body)
    
    elif command == "add-logging":
        if len(sys.argv) < 3:
            print("❌ Usage: add-logging <file>")
            return
        filepath = sys.argv[2]
        editor.add_logging_to_functions(filepath)
    
    elif command == "analyze-structure":
        if len(sys.argv) < 3:
            print("❌ Usage: analyze-structure <file>")
            return
        filepath = sys.argv[2]
        editor.analyze_code_structure(filepath)
    
    elif command == "find-functions":
        if len(sys.argv) < 3:
            print("❌ Usage: find-functions <file>")
            return
        filepath = sys.argv[2]
        functions = editor.find_functions(filepath)
        print(f"🔍 Functions found in {filepath}:")
        for func in functions:
            print(f"   Line {func['line']}: {func['name']} - {func['code']}")
    
    elif command == "find-classes":
        if len(sys.argv) < 3:
            print("❌ Usage: find-classes <file>")
            return
        filepath = sys.argv[2]
        classes = editor.find_classes(filepath)
        print(f"🔍 Classes found in {filepath}:")
        for cls in classes:
            print(f"   Line {cls['line']}: {cls['name']} - {cls['code']}")
    
    elif command == "find-methods":
        if len(sys.argv) < 3:
            print("❌ Usage: find-methods <file>")
            return
        filepath = sys.argv[2]
        methods = editor.find_methods(filepath)
        print(f"🔍 Methods found in {filepath}:")
        for method in methods:
            print(f"   Line {method['line']}: {method['name']} - {method['code']}")
    
    elif command == "find-variables":
        if len(sys.argv) < 3:
            print("❌ Usage: find-variables <file>")
            return
        filepath = sys.argv[2]
        variables = editor.find_variables(filepath)
        print(f"🔍 Variables found in {filepath}:")
        for var in variables:
            print(f"   Line {var['line']}: {var['name']} - {var['code']}")
    
    elif command == "find-imports":
        if len(sys.argv) < 3:
            print("❌ Usage: find-imports <file>")
            return
        filepath = sys.argv[2]
        imports = editor.find_imports(filepath)
        print(f"🔍 Imports found in {filepath}:")
        for imp in imports:
            print(f"   Line {imp['line']}: {imp['name']} - {imp['code']}")
    
    else:
        print(f"❌ Unknown command: {command}")
        show_help()

if __name__ == "__main__":
    main() 