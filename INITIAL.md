## FEATURE: Turn File Reader into MCP Server

Transform the existing file processing and tag generator script into an MCP-compatible server using the `mcp-framework`. This server should accept incoming tool requests for reading files and generating ctags metadata.

### Goals:
- Expose file reading and tag generation as an MCP tool.
- Support glob-based path resolution (e.g. `src/**/*.ts`).
- <PERSON><PERSON> requests via MCP and return file contents + ctags JSON as structured tool output.

### Inputs:
- `globs`: list of file paths or glob patterns.
- `output`: optional file output path.
- `max_workers`: optional concurrency limit.

### Outputs:
- JSON object of file contents.
- Per-file `.tags.json` written to the file system.
- Optionally return paths to `.tags.json` files.

### MCP Tool Name:
`file_reader_with_ctags`

### Notes:
- Should use the same logic as the standalone script.
- Consider making `generateTagsJsonForFile()` optional (toggle via flag).
- Preserve support for relative and absolute paths.

[Provide and explain examples that you have in the `examples/` folder]

## DOCUMENTATION:
https://github.com/QuantGeekDev/mcp-framework/tree/main
Introduction	https://mcp-framework.com/docs/introduction
Installation	https://mcp-framework.com/docs/installation
Quickstart	https://mcp-framework.com/docs/quickstart
HTTP Quickstart	https://mcp-framework.com/docs/http-quickstart
Debugging	https://mcp-framework.com/docs/debugging
Authentication	https://mcp-framework.com/docs/Authentication/overview
US Treasury Fiscal Data Example	https://mcp-framework.com/docs/Examples/fiscal-data
Prompts Overview	https://mcp-framework.com/docs/Prompts/prompts-overview
Resources Overview	https://mcp-framework.com/docs/Resources/resources-overview
Tools Overview	https://mcp-framework.com/docs/Tools/tools-overview
API Integration	https://mcp-framework.com/docs/Tools/api-integration
Transport Overview	https://mcp-framework.com/docs/Transports/transports-overview
STDIO Transport	https://mcp-framework.com/docs/Transports/stdio-transport
HTTP Stream Transport	https://mcp-framework.com/docs/Transports/http-stream-transport
SSE Transport	https://mcp-framework.com/docs/Transports/sse
Server Configuration	https://mcp-framework.com/docs/server-configuration


## OTHER CONSIDERATIONS:

[Any other considerations or specific requirements - great place to include gotchas that you see AI coding assistants miss with your projects a lot]
